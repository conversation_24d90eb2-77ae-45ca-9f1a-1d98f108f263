package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.resp.IndexEnhanceDataOverviewResp;
import com.hvisions.productBoard.resp.IndexEnhanceKeyOrderResp;
import com.hvisions.productBoard.resp.PmEnhanceCompleteRateResp;
import com.hvisions.productBoard.service.IndexEnhanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "生产首页 - 增强")
@RestController
@RequiredArgsConstructor
@RequestMapping("/index/enhance")
public class IndexEnhanceController {

    @Resource
    private IndexEnhanceService indexEnhanceService;

    @ApiOperation(value = "数据概览")
    @GetMapping("/dataOverview")
    public IndexEnhanceDataOverviewResp getDataOverview() {
        return indexEnhanceService.getDataOverview();
    }

    @ApiOperation(value = "增强计划达成率（年）")
    @GetMapping("/rateYear")
    public List<PmEnhanceCompleteRateResp> getRateYear() {
        return indexEnhanceService.getCompleteRate(TimePeriodEnum.YEAR);
    }

    @ApiOperation(value = "增强计划达成率（月）")
    @GetMapping("/rateMonth")
    public List<PmEnhanceCompleteRateResp> getRateMonth() {
        return indexEnhanceService.getCompleteRate(TimePeriodEnum.MONTH);
    }

    @ApiOperation(value = "增强计划达成率（周）")
    @GetMapping("/rateWeek")
    public List<PmEnhanceCompleteRateResp> getRateWeek() {
        return indexEnhanceService.getCompleteRate(TimePeriodEnum.WEEK);
    }

    @ApiOperation(value = "关键订单实时监控")
    @PostMapping("/keyOrder")
    public List<IndexEnhanceKeyOrderResp> getKeyOrder() {
        return indexEnhanceService.getKeyOrder();
    }

    @ApiOperation(value = "关键原料库存")
    @GetMapping("/keyMaterials")
    public String getKeyMaterials() {
        return indexEnhanceService.getKeyMaterials();
    }

    @ApiOperation(value = "公告")
    @GetMapping("/notice")
    public String getNotice() {
        return indexEnhanceService.getNotice();
    }
}
