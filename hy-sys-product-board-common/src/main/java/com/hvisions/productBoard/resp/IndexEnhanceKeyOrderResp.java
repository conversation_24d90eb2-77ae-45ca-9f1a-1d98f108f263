package com.hvisions.productBoard.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class IndexEnhanceKeyOrderResp {
    @ApiModelProperty(value = "生产任务单号")
    private String orderCode;
    @ApiModelProperty(value = "产品名称")
    private String productType;
    @ApiModelProperty(value = "规格型号")
    private String stepName;
    @ApiModelProperty(value = "订单数量")
    private Double finishedQuantity;
    @ApiModelProperty(value = "产品批次")
    private Double planQuantity;
    @ApiModelProperty(value = "")
    private Double percentage;
}
