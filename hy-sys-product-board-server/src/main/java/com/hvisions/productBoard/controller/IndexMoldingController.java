package com.hvisions.productBoard.controller;

import com.hvisions.productBoard.resp.IndexMoldingDataOverviewResp;
import com.hvisions.productBoard.service.IndexMoldingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "生产首页 - 成型")
@RestController
@RequiredArgsConstructor
@RequestMapping("/index/molding")
public class IndexMoldingController {

    @Resource
    private IndexMoldingService indexMoldingService;

    @ApiOperation(value = "数据概览")
    @GetMapping("/dataOverview")
    public IndexMoldingDataOverviewResp getDataOverview() {
        return indexMoldingService.getDataOverview();
    }

    @ApiOperation(value = "生产订单")
    @PostMapping("/keyOrder")
    public String getKeyOrder() {
        return indexMoldingService.getKeyOrder();
    }

    @ApiOperation(value = "试制订单")
    @PostMapping("/testOrder")
    public String getTestOrder() {
        return indexMoldingService.getTestOrder();
    }

    @ApiOperation(value = "关键原料库存")
    @GetMapping("/keyMaterials")
    public String getKeyMaterials() {
        return indexMoldingService.getKeyMaterials();
    }

    @ApiOperation(value = "公告")
    @GetMapping("/notice")
    public String getNotice() {
        return indexMoldingService.getNotice();
    }
}
