package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.productBoard.entity.HyPmMoldingWorkOrder;
import com.hvisions.productBoard.enums.MoldingWorkOrderStateEnum;
import com.hvisions.productBoard.mapper.HyPmMoldingWorkOrderMapper;
import com.hvisions.productBoard.resp.IndexMoldingDataOverviewResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexMoldingService {
    
    @Resource
    private HyPmMoldingWorkOrderMapper hyPmMoldingWorkOrderMapper;
    
    public IndexMoldingDataOverviewResp getDataOverview() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

        List<HyPmMoldingWorkOrder> ongoingOrders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .eq(HyPmMoldingWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCING.getValue())
        );
        Integer ongoingOrder = ongoingOrders.size();

        List<HyPmMoldingWorkOrder> newOrders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .between(HyPmMoldingWorkOrder::getCreateTime, todayStart, todayEnd)
        );
        Integer newOrder = newOrders.size();

        LocalDateTime nearDate = today.plusMonths(1).atTime(LocalTime.MAX);
        List<HyPmMoldingWorkOrder> nearOrders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .between(HyPmMoldingWorkOrder::getPlanEndTime, todayStart, nearDate)
        );
        Integer nearOrder = nearOrders.size();

        List<HyPmMoldingWorkOrder> todayFinishedOrders = hyPmMoldingWorkOrderMapper.selectList(
                new QueryWrapper<HyPmMoldingWorkOrder>().lambda()
                        .eq(HyPmMoldingWorkOrder::getState, MoldingWorkOrderStateEnum.PRODUCED_FINISH.getValue())
                        .between(HyPmMoldingWorkOrder::getActualEndTime, todayStart, todayEnd)
        );
        Integer todayFinishOrder = todayFinishedOrders.size();

        BigDecimal todayOutput = todayFinishedOrders.stream()
                .map(order -> order.getFinishedQuantity() != null ? order.getFinishedQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return IndexMoldingDataOverviewResp.builder()
                .ongoingOrder(ongoingOrder)
                .newOrder(newOrder)
                .nearOrder(nearOrder)
                .todayFinishOrder(todayFinishOrder)
                .todayOutput(todayOutput)
                .build();
    }

    public String getKeyOrder() {
        return null;
    }

    public String getTestOrder() {
        return null;
    }

    public String getKeyMaterials() {
        return null;
    }

    public String getNotice() {
        return null;
    }
}
