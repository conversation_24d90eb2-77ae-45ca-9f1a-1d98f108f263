package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hvisions.productBoard.entity.HyPmEnhanceWorkOrder;
import com.hvisions.productBoard.enums.EnhanceWorkOrderStateEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.HyPmEnhanceWorkOrderMapper;
import com.hvisions.productBoard.resp.IndexEnhanceDataOverviewResp;
import com.hvisions.productBoard.resp.IndexEnhanceKeyOrderResp;
import com.hvisions.productBoard.resp.PmEnhanceCompleteRateResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexEnhanceService {

    @Resource
    private PmEnhanceService pmEnhanceService;

    @Resource
    private HyPmEnhanceWorkOrderMapper hyPmEnhanceWorkOrderMapper;

    public IndexEnhanceDataOverviewResp getDataOverview() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

        List<HyPmEnhanceWorkOrder> ongoingOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCING.getValue())
        );
        Integer ongoingOrder = ongoingOrders.size();

        List<HyPmEnhanceWorkOrder> newOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .between(HyPmEnhanceWorkOrder::getCreateTime, todayStart, todayEnd)
        );
        Integer newOrder = newOrders.size();

        LocalDateTime nearDate = today.plusMonths(1).atTime(LocalTime.MAX);
        List<HyPmEnhanceWorkOrder> nearOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .between(HyPmEnhanceWorkOrder::getPlanEndTime, todayStart, nearDate)
        );
        Integer nearOrder = nearOrders.size();

        List<HyPmEnhanceWorkOrder> todayFinishedOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCED.getValue())
                        .between(HyPmEnhanceWorkOrder::getActualEndTime, todayStart, todayEnd)
        );
        Integer todayFinishOrder = todayFinishedOrders.size();

        BigDecimal todayOutput = todayFinishedOrders.stream()
                .map(order -> order.getFinishedQuantity() != null ? order.getFinishedQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return IndexEnhanceDataOverviewResp.builder()
                .ongoingOrder(ongoingOrder)
                .newOrder(newOrder)
                .nearOrder(nearOrder)
                .todayFinishOrder(todayFinishOrder)
                .todayOutput(todayOutput)
                .build();
    }

    public List<PmEnhanceCompleteRateResp> getCompleteRate(TimePeriodEnum periodEnum) {
        return pmEnhanceService.getCompleteRate(periodEnum);
    }

    public List<IndexEnhanceKeyOrderResp> getKeyOrder() {
        log.info("开始查询关键订单实时监控数据");

        // 查询状态为PRODUCING（生产中）的增强生产任务单，按计划结束时间升序排序
        List<HyPmEnhanceWorkOrder> ongoingOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCING.getValue())
                        .orderByAsc(HyPmEnhanceWorkOrder::getPlanEndTime)
        );

        log.info("查询到进行中的任务单数量: {}", ongoingOrders.size());

        // 转换为响应对象
        List<IndexEnhanceKeyOrderResp> result = ongoingOrders.stream()
                .map(this::convertToKeyOrderResp)
                .collect(Collectors.toList());

        log.info("成功转换关键订单数据，返回数量: {}", result.size());
        return result;
    }

    /**
     * 将HyPmEnhanceWorkOrder转换为IndexEnhanceKeyOrderResp
     */
    private IndexEnhanceKeyOrderResp convertToKeyOrderResp(HyPmEnhanceWorkOrder order) {
        // 计算完成百分比，处理除零情况
        Double percentage = 0.0;
        if (order.getPlanQuantity() != null && order.getPlanQuantity().doubleValue() > 0) {
            Double finishedQty = order.getFinishedQuantity() != null ? order.getFinishedQuantity().doubleValue() : 0.0;
            percentage = (finishedQty / order.getPlanQuantity().doubleValue()) * 100;
        }

        return IndexEnhanceKeyOrderResp.builder()
                .orderCode(order.getCode())
                .productType(order.getProductType())
                .stepName(order.getMaterialEigenvalue()) // 规格型号使用产品规格字段
                .finishedQuantity(order.getFinishedQuantity() != null ? order.getFinishedQuantity().doubleValue() : 0.0)
                .planQuantity(order.getPlanQuantity() != null ? order.getPlanQuantity().doubleValue() : 0.0)
                .percentage(percentage)
                .build();
    }

    public String getKeyMaterials() {
        return null;
    }

    public String getNotice() {
        return null;
    }
}
