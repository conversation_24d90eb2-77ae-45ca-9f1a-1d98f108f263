package com.hvisions.productBoard.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hvisions.productBoard.entity.HyPmEnhanceWorkOrder;
import com.hvisions.productBoard.enums.EnhanceWorkOrderStateEnum;
import com.hvisions.productBoard.enums.TimePeriodEnum;
import com.hvisions.productBoard.mapper.HyPmEnhanceWorkOrderMapper;
import com.hvisions.productBoard.resp.IndexEnhanceDataOverviewResp;
import com.hvisions.productBoard.resp.IndexEnhanceKeyOrderResp;
import com.hvisions.productBoard.resp.PmEnhanceCompleteRateResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class IndexEnhanceService {

    @Resource
    private PmEnhanceService pmEnhanceService;

    @Resource
    private HyPmEnhanceWorkOrderMapper hyPmEnhanceWorkOrderMapper;

    public IndexEnhanceDataOverviewResp getDataOverview() {
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LocalDateTime todayEnd = today.atTime(LocalTime.MAX);

        List<HyPmEnhanceWorkOrder> ongoingOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCING.getValue())
        );
        Integer ongoingOrder = ongoingOrders.size();

        List<HyPmEnhanceWorkOrder> newOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .between(HyPmEnhanceWorkOrder::getCreateTime, todayStart, todayEnd)
        );
        Integer newOrder = newOrders.size();

        LocalDateTime nearDate = today.plusMonths(1).atTime(LocalTime.MAX);
        List<HyPmEnhanceWorkOrder> nearOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .between(HyPmEnhanceWorkOrder::getPlanEndTime, todayStart, nearDate)
        );
        Integer nearOrder = nearOrders.size();

        List<HyPmEnhanceWorkOrder> todayFinishedOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCED.getValue())
                        .between(HyPmEnhanceWorkOrder::getActualEndTime, todayStart, todayEnd)
        );
        Integer todayFinishOrder = todayFinishedOrders.size();

        BigDecimal todayOutput = todayFinishedOrders.stream()
                .map(order -> order.getFinishedQuantity() != null ? order.getFinishedQuantity() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return IndexEnhanceDataOverviewResp.builder()
                .ongoingOrder(ongoingOrder)
                .newOrder(newOrder)
                .nearOrder(nearOrder)
                .todayFinishOrder(todayFinishOrder)
                .todayOutput(todayOutput)
                .build();
    }

    public List<PmEnhanceCompleteRateResp> getCompleteRate(TimePeriodEnum periodEnum) {
        return pmEnhanceService.getCompleteRate(periodEnum);
    }

    public Page<IndexEnhanceKeyOrderResp> getKeyOrder(Integer current, Integer size) {
        log.info("开始查询关键订单实时监控数据，页码: {}, 每页大小: {}", current, size);

        // 查询状态为PRODUCING（生产中）的增强生产任务单，按计划结束时间升序排序
        List<HyPmEnhanceWorkOrder> ongoingOrders = hyPmEnhanceWorkOrderMapper.selectList(
                new QueryWrapper<HyPmEnhanceWorkOrder>().lambda()
                        .eq(HyPmEnhanceWorkOrder::getState, EnhanceWorkOrderStateEnum.PRODUCING.getValue())
                        .orderByAsc(HyPmEnhanceWorkOrder::getPlanEndTime)
        );

        log.info("查询到进行中的任务单数量: {}", ongoingOrders.size());

        // 转换为响应对象列表
        List<IndexEnhanceKeyOrderResp> allRecords = ongoingOrders.stream()
                .map(order -> {
                    // 计算完成百分比，处理除零情况
                    Double percentage = 0.0;
                    if (order.getPlanQuantity() != null && order.getPlanQuantity().doubleValue() > 0) {
                        Double finishedQty = order.getFinishedQuantity() != null ? order.getFinishedQuantity().doubleValue() : 0.0;
                        percentage = (finishedQty / order.getPlanQuantity().doubleValue()) * 100;
                    }

                    return IndexEnhanceKeyOrderResp.builder()
                            .orderCode(order.getCode())
                            .productName(order.getMaterialName())
                            .productEigenvalue(order.getMaterialEigenvalue())
                            .orderQuantity(order.getPlanQuantity() != null ? order.getPlanQuantity().intValue() : 0)
                            .batchNum(order.getMaterialBatchNum())
                            .percentage(percentage)
                            .productionQuantity(order.getFinishedQuantity() != null ? order.getFinishedQuantity() : BigDecimal.ZERO)
                            .build();
                })
                .collect(Collectors.toList());

        // 手动分页处理
        int total = allRecords.size();
        int offset = (current - 1) * size;

        List<IndexEnhanceKeyOrderResp> pageRecords;
        if (offset >= total) {
            // 超出范围，返回空列表
            pageRecords = new ArrayList<>();
        } else {
            int endIndex = Math.min(offset + size, total);
            pageRecords = allRecords.subList(offset, endIndex);
        }

        // 构建分页对象
        Page<IndexEnhanceKeyOrderResp> resultPage = new Page<>(current, size);
        resultPage.setRecords(pageRecords);
        resultPage.setTotal(total);

        log.info("成功转换关键订单数据，总数: {}, 当前页数据量: {}", total, pageRecords.size());
        return resultPage;
    }

    public String getKeyMaterials() {
        return null;
    }

    public String getNotice() {
        return null;
    }
}
